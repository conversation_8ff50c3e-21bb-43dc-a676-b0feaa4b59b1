"use client"

import { useState, useRef } from 'react'
import { CldUploadWidget } from 'next-cloudinary'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Upload, X, Image as ImageIcon } from 'lucide-react'
import Image from 'next/image'

interface ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  onRemove?: () => void
  disabled?: boolean
  folder?: string
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled,
  folder = 'anjali-cms'
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)

  const onUpload = (result: any) => {
    setIsUploading(false)
    onChange(result.info.secure_url)
  }

  const onUploadError = (error: any) => {
    setIsUploading(false)
    console.error('Upload error:', error)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <CldUploadWidget
          uploadPreset="ml_default" // You'll need to create this in Cloudinary
          options={{
            folder,
            maxFiles: 1,
            resourceType: 'image',
            clientAllowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            maxFileSize: 5000000, // 5MB
          }}
          onUpload={onUpload}
          onError={onUploadError}
        >
          {({ open }) => (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsUploading(true)
                open()
              }}
              disabled={disabled || isUploading}
            >
              <Upload className="h-4 w-4 mr-2" />
              {isUploading ? 'Uploading...' : 'Upload Image'}
            </Button>
          )}
        </CldUploadWidget>

        {value && onRemove && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onRemove}
            disabled={disabled}
          >
            <X className="h-4 w-4 mr-2" />
            Remove
          </Button>
        )}
      </div>

      {value && (
        <div className="relative w-full max-w-sm">
          <div className="relative aspect-video w-full overflow-hidden rounded-lg border">
            <Image
              src={value}
              alt="Uploaded image"
              fill
              className="object-cover"
            />
          </div>
        </div>
      )}

      {!value && (
        <div className="flex items-center justify-center w-full max-w-sm h-32 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="text-center">
            <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500">No image selected</p>
          </div>
        </div>
      )}
    </div>
  )
}

// Alternative simple file input version
export function SimpleImageUpload({
  value,
  onChange,
  disabled,
}: {
  value?: string
  onChange: (url: string) => void
  disabled?: boolean
}) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })
      
      if (response.ok) {
        const data = await response.json()
        onChange(data.url)
      } else {
        throw new Error('Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="image-upload">Image</Label>
        <Input
          id="image-upload"
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          disabled={disabled || isUploading}
          ref={fileInputRef}
        />
      </div>
      
      {value && (
        <div className="relative w-full max-w-sm">
          <Image
            src={value}
            alt="Preview"
            width={300}
            height={200}
            className="rounded-lg object-cover"
          />
        </div>
      )}
    </div>
  )
}
