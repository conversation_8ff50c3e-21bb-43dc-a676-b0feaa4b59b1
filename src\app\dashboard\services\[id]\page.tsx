"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Edit, ArrowLeft, Calendar, Clock, DollarSign, Star, CheckCircle } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { toast } from 'sonner'

interface Service {
  id: string
  title: string
  slug: string
  description: string
  features: string[]
  duration?: string
  price?: string
  image?: string
  category?: string
  popular: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  createdAt: string
  updatedAt: string
}

export default function ServiceViewPage() {
  const params = useParams()
  const router = useRouter()
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchService = async () => {
      try {
        const response = await fetch(`/api/services/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setService(data)
        } else {
          toast.error('Service not found')
          router.push('/dashboard/services')
        }
      } catch (error) {
        toast.error('Error fetching service')
        router.push('/dashboard/services')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchService()
    }
  }, [params.id, router])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!service) {
    return <div>Service not found</div>
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'default',
      INACTIVE: 'secondary',
      ARCHIVED: 'outline',
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {status}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              {service.title}
              {service.popular && (
                <Star className="h-6 w-6 text-yellow-500 fill-current" />
              )}
            </h1>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Created {formatDate(service.createdAt)}
              </div>
              {service.duration && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {service.duration}
                </div>
              )}
              {service.price && (
                <div className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  {service.price}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(service.status)}
          {service.category && (
            <Badge variant="outline">{service.category}</Badge>
          )}
          <Button asChild>
            <Link href={`/dashboard/services/${service.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {service.image && (
            <Card>
              <CardContent className="p-0">
                <div className="relative aspect-video w-full overflow-hidden rounded-lg">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                {service.description}
              </p>
            </CardContent>
          </Card>

          {service.features.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Features & Benefits</CardTitle>
                <CardDescription>
                  What's included in this service
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Service Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="mt-1">
                  {getStatusBadge(service.status)}
                </div>
              </div>

              {service.category && (
                <div>
                  <Label className="text-sm font-medium">Category</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{service.category}</Badge>
                  </div>
                </div>
              )}

              {service.duration && (
                <div>
                  <Label className="text-sm font-medium">Duration</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {service.duration}
                  </p>
                </div>
              )}

              {service.price && (
                <div>
                  <Label className="text-sm font-medium">Price</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {service.price}
                  </p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">Popular Service</Label>
                <div className="mt-1">
                  <Badge variant={service.popular ? "default" : "outline"}>
                    {service.popular ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>

              <Separator />

              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {formatDate(service.createdAt)}
                </div>
                <div>
                  <span className="font-medium">Updated:</span>{' '}
                  {formatDate(service.updatedAt)}
                </div>
                <div>
                  <span className="font-medium">Slug:</span>{' '}
                  <code className="text-xs bg-muted px-1 py-0.5 rounded">
                    {service.slug}
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full">
                <Link href={`/dashboard/services/${service.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Service
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/services">
                  View All Services
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>
}
