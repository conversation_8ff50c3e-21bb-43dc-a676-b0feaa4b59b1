import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug } from '@/lib/utils'
import { z } from 'zod'

const packageUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().min(1, 'Description is required').optional(),
  services: z.array(z.string()).optional(),
  features: z.array(z.string()).optional(),
  price: z.string().min(1, 'Price is required').optional(),
  originalPrice: z.string().optional(),
  duration: z.string().optional(),
  popular: z.boolean().optional(),
  image: z.string().optional(),
  category: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'ARCHIVED']).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const packageData = await prisma.package.findUnique({
      where: { id: params.id },
    })

    if (!packageData) {
      return NextResponse.json({ error: 'Package not found' }, { status: 404 })
    }

    return NextResponse.json(packageData)
  } catch (error) {
    console.error('Error fetching package:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = packageUpdateSchema.parse(body)

    const existingPackage = await prisma.package.findUnique({
      where: { id: params.id },
    })

    if (!existingPackage) {
      return NextResponse.json({ error: 'Package not found' }, { status: 404 })
    }

    let slug = existingPackage.slug
    
    // Generate new slug if name changed
    if (validatedData.name && validatedData.name !== existingPackage.name) {
      slug = generateSlug(validatedData.name)
      
      // Check if new slug already exists
      const slugExists = await prisma.package.findFirst({
        where: { 
          slug,
          id: { not: params.id }
        },
      })
      
      if (slugExists) {
        return NextResponse.json({ error: 'A package with this name already exists' }, { status: 400 })
      }
    }

    const packageData = await prisma.package.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        slug,
      },
    })

    return NextResponse.json(packageData)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error('Error updating package:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const packageData = await prisma.package.findUnique({
      where: { id: params.id },
    })

    if (!packageData) {
      return NextResponse.json({ error: 'Package not found' }, { status: 404 })
    }

    await prisma.package.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ message: 'Package deleted successfully' })
  } catch (error) {
    console.error('Error deleting package:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
