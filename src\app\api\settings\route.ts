import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const settingSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  value: z.string().min(1, 'Value is required'),
  type: z.enum(['TEXT', 'JSON', 'BOOLEAN', 'NUMBER', 'URL', 'EMAIL']).default('TEXT'),
  description: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (key) {
      // Get specific setting
      const setting = await prisma.siteConfig.findUnique({
        where: { key },
      })

      if (!setting) {
        return NextResponse.json({ error: 'Setting not found' }, { status: 404 })
      }

      return NextResponse.json(setting)
    } else {
      // Get all settings
      const settings = await prisma.siteConfig.findMany({
        orderBy: { key: 'asc' },
      })

      // Group settings by category for better organization
      const groupedSettings = settings.reduce((acc, setting) => {
        const category = setting.key.split('.')[0] || 'general'
        if (!acc[category]) {
          acc[category] = []
        }
        acc[category].push(setting)
        return acc
      }, {} as Record<string, typeof settings>)

      return NextResponse.json({
        settings,
        grouped: groupedSettings,
      })
    }
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = settingSchema.parse(body)

    // Check if setting already exists
    const existingSetting = await prisma.siteConfig.findUnique({
      where: { key: validatedData.key },
    })

    if (existingSetting) {
      return NextResponse.json({ error: 'Setting with this key already exists' }, { status: 400 })
    }

    const setting = await prisma.siteConfig.create({
      data: validatedData,
    })

    return NextResponse.json(setting, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error('Error creating setting:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Handle bulk update
    if (Array.isArray(body)) {
      const updates = body.map(async (item) => {
        const { key, value, type, description } = item
        return prisma.siteConfig.upsert({
          where: { key },
          update: { value, type, description },
          create: { key, value, type: type || 'TEXT', description },
        })
      })

      const results = await Promise.all(updates)
      return NextResponse.json(results)
    }

    // Handle single update
    const { key, value, type, description } = body

    if (!key || !value) {
      return NextResponse.json({ error: 'Key and value are required' }, { status: 400 })
    }

    const setting = await prisma.siteConfig.upsert({
      where: { key },
      update: { value, type, description },
      create: { key, value, type: type || 'TEXT', description },
    })

    return NextResponse.json(setting)
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
